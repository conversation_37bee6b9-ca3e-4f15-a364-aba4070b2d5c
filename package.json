{"name": "vite-project", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --config vite/web.config.ts", "build": "run-p type-check && vite --config vite/web.config.ts build", "preview": "vite --config vite/web.config.ts preview", "test": "vitest --config vite/vitest.config.ts", "prepare": "husky install", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.10.0", "element-plus": "^2.10.2", "md5": "^2.3.0", "pinia": "^3.0.3", "qs": "^6.14.0", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/mdi": "^1.2.3", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/md5": "^2.3.5", "@types/node": "^24.0.3", "@types/qs": "^6.14.0", "@unocss/preset-rem-to-px": "^66.2.3", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-oxlint": "^1.1.0", "eslint-plugin-vue": "~10.2.0", "husky": "^9.1.7", "jiti": "^2.4.2", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "npm-run-all2": "^8.0.4", "oxlint": "^1.1.0", "prettier": "3.5.3", "typescript": "~5.8.3", "unocss": "^66.2.3", "vite": "^6.3.5", "vite-plugin-mock-dev-server": "^1.8.7", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}, "lint-staged": {"*.{ts,vue}": ["npm run lint:eslint", "npm run format"]}}