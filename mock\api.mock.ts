import { defineMock } from 'vite-plugin-mock-dev-server';
import { TableOrderInfo, TablePositionInfo, TableTradeRecordInfo } from '../src/types';
import {
  PositionDirectionEnum,
  TradeDirectionEnum,
  OrderStatusEnum,
  PositionEffectEnum,
} from '../src/enum';

// 模拟持仓数据
const mockPositions: TablePositionInfo[] = [
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '600036',
    instrumentName: '招商银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -948.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '600519',
    instrumentName: '贵州茅台',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -862.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601398',
    instrumentName: '工商银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -948.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601288',
    instrumentName: '农业银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: 1824.72,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '601988',
    instrumentName: '中国银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -862.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601628',
    instrumentName: '中国人寿',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -948.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '601318',
    instrumentName: '中国平安',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -862.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
];

// 模拟委托数据
const mockOrders: TableOrderInfo[] = [
  {
    id: 1,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    instrument: '600036',
    instrumentName: '招商银行',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 0,
    tradedAmount: 3132,
    tradedVolume: 100,
    tradedPrice: 31.32,
    cancelledVolume: 0,
    volumeOriginal: 100,
    commission: 40,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.全成,
    localOrderId: 'L2023010100001',
    exchangeOrderId: 'E2023010100001',
    orderPrice: 31.32,
    orderPriceType: 1,
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 09:30:00',
    tradingDay: '2023-01-01',
    customId: 1,
    businessFlag: 0,
  },
  {
    id: 2,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    instrument: '600519',
    instrumentName: '贵州茅台',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 0,
    tradedAmount: 0,
    tradedVolume: 0,
    tradedPrice: 0,
    cancelledVolume: 100,
    volumeOriginal: 100,
    commission: 0,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.已撤,
    localOrderId: 'L2023010100002',
    exchangeOrderId: 'E2023010100002',
    orderPrice: 1800.0,
    orderPriceType: 1,
    direction: TradeDirectionEnum.卖出,
    positionEffect: PositionEffectEnum.平仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 10:15:00',
    tradingDay: '2023-01-01',
    customId: 2,
    businessFlag: 0,
  },
  {
    id: 3,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    instrument: '601398',
    instrumentName: '工商银行',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 0,
    tradedAmount: 1566,
    tradedVolume: 50,
    tradedPrice: 31.32,
    cancelledVolume: 50,
    volumeOriginal: 100,
    commission: 20,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.废单,
    localOrderId: 'L2023010100003',
    exchangeOrderId: 'E2023010100003',
    orderPrice: 31.32,
    orderPriceType: 1,
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 11:05:00',
    tradingDay: '2023-01-01',
    customId: 3,
    businessFlag: 0,
  },
  {
    id: 4,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    instrument: '601288',
    instrumentName: '农业银行',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 100,
    tradedAmount: 0,
    tradedVolume: 0,
    tradedPrice: 0,
    cancelledVolume: 0,
    volumeOriginal: 100,
    commission: 0,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.未成交,
    localOrderId: 'L2023010100004',
    exchangeOrderId: 'E2023010100004',
    orderPrice: 3.5,
    orderPriceType: 1,
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 13:30:00',
    tradingDay: '2023-01-01',
    customId: 4,
    businessFlag: 0,
  },
  {
    id: 5,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    instrument: '601988',
    instrumentName: '中国银行',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 0,
    tradedAmount: 3132,
    tradedVolume: 100,
    tradedPrice: 31.32,
    cancelledVolume: 0,
    volumeOriginal: 100,
    commission: 40,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.部分成交,
    localOrderId: 'L2023010100005',
    exchangeOrderId: 'E2023010100005',
    orderPrice: 31.32,
    orderPriceType: 1,
    direction: TradeDirectionEnum.卖出,
    positionEffect: PositionEffectEnum.平仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 14:20:00',
    tradingDay: '2023-01-01',
    customId: 5,
    businessFlag: 0,
  },
];

// 模拟成交数据
const mockTradeRecords: TableTradeRecordInfo[] = [
  {
    tradeId: 'T2023010100001',
    exchangeOrderId: 'E2023010100001',
    orderId: 1,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-01',
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    instrument: '600036',
    instrumentName: '招商银行',
    tradeTime: '2023-01-01 09:30:15',
    volume: 100,
    tradedPrice: 31.32,
  },
  {
    tradeId: 'T2023010100002',
    exchangeOrderId: 'E2023010100003',
    orderId: 3,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-01',
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    instrument: '601398',
    instrumentName: '工商银行',
    tradeTime: '2023-01-01 11:05:30',
    volume: 50,
    tradedPrice: 31.32,
  },
  {
    tradeId: 'T2023010100003',
    exchangeOrderId: 'E2023010100005',
    orderId: 5,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-01',
    direction: TradeDirectionEnum.卖出,
    positionEffect: PositionEffectEnum.平仓,
    instrument: '601988',
    instrumentName: '中国银行',
    tradeTime: '2023-01-01 14:20:45',
    volume: 100,
    tradedPrice: 31.32,
  },
  {
    tradeId: 'T2023010200001',
    exchangeOrderId: 'E2023010200001',
    orderId: 6,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-02',
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    instrument: '601628',
    instrumentName: '中国人寿',
    tradeTime: '2023-01-02 09:45:10',
    volume: 200,
    tradedPrice: 42.56,
  },
  {
    tradeId: 'T2023010200002',
    exchangeOrderId: 'E2023010200002',
    orderId: 7,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-02',
    direction: TradeDirectionEnum.卖出,
    positionEffect: PositionEffectEnum.平仓,
    instrument: '601318',
    instrumentName: '中国平安',
    tradeTime: '2023-01-02 14:30:25',
    volume: 150,
    tradedPrice: 56.78,
  },
];

export default defineMock([
  {
    url: '/api/login',
    method: 'POST',
    body: req => {
      return {
        err: 0,
        data: {
          token: '**********',
          username: req.body.username,
          role: 'admin',
        },
      };
    },
  },
  {
    url: '/api/positions',
    method: 'GET',
    body: req => {
      console.log(req.query);
      return {
        err: 0,
        data: mockPositions,
      };
    },
  },
  {
    url: '/api/orders',
    method: 'GET',
    body: req => {
      console.log(req.query);
      // 从mockOrders随机选择订单并生成指定count数量的数据
      const generateMockOrders = (count: number) => {
        return Array.from({ length: count }, () => {
          return {
            ...mockOrders[Math.floor(Math.random() * mockOrders.length)],
            id: Math.random(),
            orderPrice: Number((Math.random() * 100).toFixed(2)),
            orderTime: (() => {
              const date = new Date();
              date.setHours(Math.floor(Math.random() * 24));
              date.setMinutes(Math.floor(Math.random() * 60));
              date.setSeconds(Math.floor(Math.random() * 60));
              return date.toISOString().replace('T', ' ').slice(0, 19);
            })(),
          };
        });
      };
      return {
        err: 0,
        data: generateMockOrders(1000),
      };
    },
  },
  {
    url: '/api/trades',
    method: 'GET',
    body: req => {
      console.log(req.query);
      return {
        err: 0,
        data: mockTradeRecords,
      };
    },
  },
]);
