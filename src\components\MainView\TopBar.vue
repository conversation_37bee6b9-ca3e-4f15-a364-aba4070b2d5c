<script setup lang="ts">
import { shallowRef } from 'vue';
import router from '@/router';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores';
import UserDialog from '../UserDialog.vue';

const store = useUserStore();
const { userInfo } = storeToRefs(store);
const { clearUserInfo } = store;
const visible = shallowRef(false);

const handleCommand = (command: string) => {
  if (command === 'logout') {
    clearUserInfo();
    router.push({ name: 'login' });
  } else if (command === 'edit') {
    visible.value = true;
  }
};
</script>

<template>
  <div px-10 h-40 flex aic jcsb bg="[--g-panel-bg]">
    <div c-white fs-18>稳步前行</div>
    <div flex aic gap-10>
      <span c-white>{{ userInfo.user_name }}</span>
      <el-dropdown @command="handleCommand">
        <i mr-10 fs-26 i-mdi-account-circle cursor-pointer />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="edit">修改</el-dropdown-item>
            <el-dropdown-item command="logout">退出</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <UserDialog v-model="visible" :user="userInfo" />
  </div>
</template>

<style scoped></style>
